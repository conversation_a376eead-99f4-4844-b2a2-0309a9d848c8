import datetime
import json
import logging
import time

from common.client.http_client import HTTPClient

logger = logging.getLogger(__name__)


class CVATClient(HTTPClient):
    def __init__(self, gateway, token):
        base_url = gateway + 'cv'
        super().__init__(base_url, token)

    def process_tasks_from_task(self, origin_id: str, assets: list, project_id: str, tenantId: str):
        """
          从指定任务中新建任务: id 原始任务id, assets 需要处理的资产列表
        """
        url = "/api/process/tasks/from-task"
        # 生成新的 name 值
        timestamp = datetime.datetime.now().strftime("%Y%m%d%H%M%S")
        name = f"{origin_id}_{timestamp}_auto-scheduler"

        params = {
            "project_id": project_id,
            "tenantId": tenantId,
            "skipReview": True
        }

        body = {
            "id": origin_id,
            "name": name,
            "assets": assets
        }

        ok, code, content = self.post(url, body, params)
        if ok:
            content = json.loads(content)
            return content.get('id', None), code  # 使用 get 方法避免 KeyError
        else:
            raise Exception(f'code: {code}, msg: {content}')

    def start_task(self, taskId: str, stepType: str, project_id: str, tenantId: str):
        """
          启动指定任务
        """
        url = f"/api/process/tasks/{taskId}/start"
        params = {
            "stepType": stepType,  # 可选参数，如果为空则表示从头开始执行
            "project_id": project_id,
            "tenantId": tenantId,
        }
        ok, code, content = self.post(url, params=params)
        if ok:
            content = json.loads(content)
            return content, code
        else:
            raise Exception(f'code: {code}, msg: {content}')

    def get_task_detail(self, taskId: str):
        """
          获取任务详细信息(轮训查询任务状态,status为IMPORT_REVIEW, 调用导出接口,值为IMPORT_PENDING,调applet接口入库)
        """
        url = f"/api/process/tasks/{taskId}/detail"

        ok, code, content = self.get(url)
        if ok:
            content = json.loads(content)
            status = content.get('status', None)  # 获取 status 字段的值
            index_config = content.get('indexConfig', None)
            return status, index_config
        else:
            raise Exception(f'code: {code}, msg: {content}')

    def export(self, taskId: str):
        """
          导出任务数据到文件系统
        """
        url = f"/api/process/tasks/{taskId}/export"

        ok, code, content = self.post(url)
        if ok:
            return code
        else:
            raise Exception(f'code: {code}, msg: {content}')

    def get_process_job_id(self, taskId: str):
        """
          获取入库审核任务
        """
        url = f"/api/process/review/import?taskId={taskId}"

        ok, code, content = self.get(url)
        if ok:
            content = json.loads(content)
            job_id = content.get('id', None)  # 获取 job_id 字段的值
            return job_id, code
        else:
            raise Exception(f'code: {code}, msg: {content}')

    def pass_review_task(self, job_id: str):
        """
          一键通过审核任务，传参job id
        """
        url = f"/api/process/review/{job_id}/asset/all"

        ok, code, content = self.post(url)
        if ok:
            return code
        else:
            raise Exception(f'code: {code}, msg: {content}')

    def complete_review_task(self, job_id: str):
        """
          完成审核任务，传参job id
        """
        url = f"/api/process/review/{job_id}/complete"

        ok, code, content = self.post(url)
        if ok:
            return code
        else:
            raise Exception(f'code: {code}, msg: {content}')

    def wait_for_status(self, task_id: str, expected_status: str, max_retries=72, interval=100):
        """
        轮询等待任务状态变为 expected_status
        """
        retry_count = 0
        while retry_count < max_retries:
            status, index_config = self.get_task_detail(task_id)
            logger.info(f"task status: {status}, index_config: {index_config}")
            if status == expected_status:
                return True
            else:
                logger.info(f"Current status: {status}, waiting for {expected_status}...")
                time.sleep(interval)
                retry_count += 1
        raise TimeoutError(f"Timeout waiting for status: {expected_status}")
